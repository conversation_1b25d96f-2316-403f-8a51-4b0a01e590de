<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/colorAccent"
    tools:context=".ui.main.MainActivity">

   <LinearLayout
       android:layout_width="match_parent"
       android:layout_height="match_parent"
       android:orientation="vertical"
       tools:ignore="MissingConstraints">
       <com.google.android.material.appbar.AppBarLayout
           android:layout_width="match_parent"
           android:layout_height="wrap_content"
           app:elevation="0dp"
           android:theme="@style/Theme.Despreaddemo.AppBarOverlay"
           >
           <androidx.appcompat.widget.Toolbar
               android:id="@+id/toolbar"
               android:layout_width="match_parent"
               android:layout_height="?attr/actionBarSize"
               android:background="@color/white"
               app:popupTheme="@style/Theme.Despreaddemo.PopupOverlay" />

       </com.google.android.material.appbar.AppBarLayout>
       <!--          layout_constraintTop_toBottomOf-->
       <include layout="@layout/content_main" android:id="@+id/main"/>
   </LinearLayout>


</androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
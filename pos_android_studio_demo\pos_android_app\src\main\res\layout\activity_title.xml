<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data>
        <variable
            name="viewModel"
            type="com.dspread.pos.ui.payment.PaymentViewModel" />
    </data>

    <androidx.appcompat.widget.Toolbar
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:elevation="4dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:padding="16dp"
                android:src="@mipmap/left"
                android:onClick="@{() -> viewModel.onBackPressed()}"
             />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@{viewModel.titleText}"
                android:textColor="@color/white"
                android:textSize="18sp"
                />

        </RelativeLayout>

    </androidx.appcompat.widget.Toolbar>
</layout>
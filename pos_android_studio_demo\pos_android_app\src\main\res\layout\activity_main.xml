<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:binding="http://schemas.android.com/apk/res-auto">
    <data>

        <variable
            name="viewModel"
            type="com.dspread.pos.ui.main.MainViewModel" />
    </data>
<androidx.drawerlayout.widget.DrawerLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:openDrawer="start"
    binding:onDrawerOpenedCommand="@{viewModel.onDrawerOpenedCommand}"
    binding:onDrawerClosedCommand="@{viewModel.onDrawerClosedCommand}">

<!--    <include-->
<!--        android:id="@+id/app_bar_main"-->
<!--        layout="@layout/app_bar_main"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent" />-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="?attr/colorPrimary" />

        </com.google.android.material.appbar.AppBarLayout>

        <!-- 替换 NavHostFragment 为 FrameLayout -->
        <FrameLayout
            android:id="@+id/nav_host_fragment"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        app:headerLayout="@layout/nav_header_main"
        app:menu="@menu/activity_main_drawer"
        app:itemIconTint="@color/nav_item_color"
        app:itemTextColor="@color/nav_item_color"
        binding:onNavigationItemSelectedCommand="@{viewModel.switchFragmentCommand}"
        />


</androidx.drawerlayout.widget.DrawerLayout>
</layout>
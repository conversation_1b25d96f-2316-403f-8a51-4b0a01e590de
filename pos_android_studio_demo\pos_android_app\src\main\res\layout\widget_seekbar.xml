<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical">

    <TextView
        android:id="@+id/sb_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/graye3"
        android:padding="10dp"
        android:text="title"
        android:textSize="16sp"
        android:textColor="@color/black"
        tools:ignore="HardcodedText" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@drawable/account_line"/>


    <SeekBar
        android:id="@+id/sb_seekbar"
        android:layout_marginTop="20dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="3dp"
        android:thumb="@mipmap/control"
        android:progressDrawable="@drawable/print_seekbar"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/sb_start"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="left"
            android:paddingLeft="23dp"
            android:textColor="@color/black"
            android:text="1"
            tools:ignore="HardcodedText,RtlHardcoded,RtlSymmetry" />

        <TextView
            android:id="@+id/sb_result"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/black"
            android:text=""/>

        <TextView
            android:id="@+id/sb_end"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="right"
            android:paddingRight="23dp"
            android:textColor="@color/black"
            android:text="20"
            tools:ignore="HardcodedText,RtlHardcoded,RtlSymmetry" />
    </LinearLayout>


    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/graye3"/>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:padding="5dp"
            android:text="@string/cancel"
            android:textColor="@color/black"
            android:textSize="16sp"/>

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/graye3"/>

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:padding="5dp"
            android:textSize="16sp"
            android:textColor="@color/black"
            android:text="@string/confirm"/>
    </LinearLayout>
</LinearLayout>
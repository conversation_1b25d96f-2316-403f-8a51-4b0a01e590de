<?xml version="1.0" encoding="utf-8"?>
<layout >
    <data>
        <variable
            name="viewModel"
            type="com.dspread.pos.ui.home.HomeViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:binding="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/keyboard_background">

        <!-- 金额显示区域 -->
        <TextView
            android:id="@+id/txt_amount"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:gravity="end|center_vertical"
            android:padding="@dimen/spacing_normal"
            android:textSize="@dimen/text_amount"
            android:textStyle="bold"
            android:text="@{viewModel.amount}"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintHeight_percent="0.2"
            tools:text="¥0.00"/>

        <!-- 键盘区域 - 移除卡片样式，直接使用 GridLayout -->
        <androidx.gridlayout.widget.GridLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:padding="@dimen/spacing_small"
            app:columnCount="3"
            app:rowCount="4"
            app:layout_constraintTop_toBottomOf="@id/txt_amount"
            app:layout_constraintBottom_toBottomOf="parent">

            <!-- 按钮布局保持不变 -->
            <Button
                style="@style/NumberKeyboardButton"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:onClick="@{() -> viewModel.onNumberClick(`1`)}"
                android:text="1" />

            <Button
                style="@style/NumberKeyboardButton"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:onClick="@{() -> viewModel.onNumberClick(`2`)}"
                android:text="2" />

            <Button
                style="@style/NumberKeyboardButton"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:onClick="@{() -> viewModel.onNumberClick(`3`)}"
                android:text="3" />

            <Button
                style="@style/NumberKeyboardButton"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:onClick="@{() -> viewModel.onNumberClick(`4`)}"
                android:text="4" />

            <Button
                style="@style/NumberKeyboardButton"
                android:onClick="@{() -> viewModel.onNumberClick(`5`)}"
                android:text="5" />

            <Button
                style="@style/NumberKeyboardButton"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:onClick="@{() -> viewModel.onNumberClick(`6`)}"
                android:text="6" />

            <Button
                style="@style/NumberKeyboardButton"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:onClick="@{() -> viewModel.onNumberClick(`7`)}"
                android:text="7" />

            <Button
                style="@style/NumberKeyboardButton"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:onClick="@{() -> viewModel.onNumberClick(`8`)}"
                android:text="8" />

            <Button
                style="@style/NumberKeyboardButton"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:onClick="@{() -> viewModel.onNumberClick(`9`)}"
                android:text="9" />

            <!-- 清除按钮 -->
            <Button
                android:id="@+id/btn_clear"
                style="@style/NumberKeyboardButton.Clear"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:onClick="@{() -> viewModel.onClearClickCommand()}"
                android:text="C" />

            <!-- 数字 0 -->
            <Button
                style="@style/NumberKeyboardButton"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:onClick="@{() -> viewModel.onNumberClick(`0`)}"
                android:text="0" />

            <!-- 确认按钮 -->
            <Button
                android:id="@+id/btn_confirm"
                style="@style/NumberKeyboardButton.Confirm"
                android:minWidth="0dp"
                android:minHeight="0dp"
                binding:onClickCommand="@{viewModel.onConfirmClickCommand}"
                android:text="Confirm" />

        </androidx.gridlayout.widget.GridLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
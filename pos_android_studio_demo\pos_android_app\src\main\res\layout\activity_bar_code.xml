<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:binding="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="com.dspread.pos.ui.printer.activities.BarCodeViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 条形码内容 -->
                <LinearLayout
                    style="@style/LinearLayoutStyle"
                    binding:onClickCommand="@{viewModel.onContentClick}">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/content_barcode"
                        android:textSize="16sp" />

                    <TextView
                        style="@style/TextviewPrint"
                        android:text="@{viewModel.content}"
                        />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <!-- 码制选择 -->
                <LinearLayout
                    style="@style/LinearLayoutStyle"
                    binding:onClickCommand="@{viewModel.onSymbologyClick}">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/symbology_barcode"
                        android:textSize="16sp" />

                    <TextView
                        style="@style/TextviewPrint"
                        android:text="@{viewModel.symbology}"
                        />
                </LinearLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <!-- height -->
                <LinearLayout
                    style="@style/LinearLayoutStyle"
                    binding:onClickCommand="@{viewModel.onHeightClick}">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/barcode_height"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/txt_height"
                        style="@style/TextviewPrint"
                        android:text="@{viewModel.height}"
                        />
                </LinearLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <!-- width -->
                <LinearLayout
                    style="@style/LinearLayoutStyle"
                    binding:onClickCommand="@{viewModel.onWidthClick}">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/barcode_width"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/txt_width"
                        style="@style/TextviewPrint"
                        android:text="@{viewModel.width}"
                        />
                </LinearLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <!-- align -->
                <LinearLayout
                    style="@style/LinearLayoutStyle"
                    binding:onClickCommand="@{viewModel.onAlignClick}">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/set_align"
                        android:textSize="16sp" />

                    <TextView
                        style="@style/TextviewPrint"
                        android:text="@{viewModel.align}" />
                </LinearLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <!-- grayLevel -->
                <LinearLayout
                    style="@style/LinearLayoutStyle"
                    binding:onClickCommand="@{viewModel.onGrayLevelClick}">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/grayLevel"
                        android:textSize="16sp" />

                    <TextView
                        style="@style/TextviewPrint"
                        android:text="@{viewModel.grayLevel}"
                        />
                </LinearLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <!-- speedLevel -->
                <LinearLayout
                    android:id="@+id/lin_sleed_level"
                    style="@style/LinearLayoutStyle"
                    binding:onClickCommand="@{viewModel.onSpeedLevelClick}">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/speedlevel"
                        android:textSize="16sp" />

                    <TextView
                        style="@style/TextviewPrint"
                        android:text="@{viewModel.speedLevel}" />
                </LinearLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <!-- densityLevel -->
                <LinearLayout
                    android:id="@+id/lin_density_level"
                    style="@style/LinearLayoutStyle"
                    binding:onClickCommand="@{viewModel.onDensityLevelClick}">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/density_level"
                        android:textSize="16sp" />

                    <TextView
                        style="@style/TextviewPrint"
                        android:text="@{viewModel.densityLevel}" />
                </LinearLayout>

                <!-- ... 其他设置项（高度、宽度、对齐等）使用相同的布局模式 ... -->

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- 条形码预览 -->
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="16dp"
            android:scaleType="fitCenter"
            android:src="@{viewModel.barcodeBitmap}" />

    </LinearLayout>
</layout>
<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:showIn="navigation_view">

<!--    <group android:checkableBehavior="single">-->
    <group android:checkableBehavior="single"
        >
        <item
            android:id="@+id/nav_home"
            android:icon="@drawable/payment_icon"
            android:title="@string/menu_payment"
            />
<!--        <item-->
<!--            android:id="@+id/nav_autotrade"-->
<!--            android:icon="@drawable/autotrade"-->
<!--            android:title="@string/auto_trade"-->
<!--            />-->
        <item
            android:id="@+id/nav_printer"
            android:icon="@drawable/print"
            android:title="@string/printer" />
        <item
            android:id="@+id/nav_scan"
            android:icon="@drawable/scan"
            android:title="@string/scan"
            />

        <item
            android:id="@+id/nav_setting"
            android:icon="@drawable/setting_icon"
            android:title="@string/menu_setting">
        </item>

    </group>


</menu>
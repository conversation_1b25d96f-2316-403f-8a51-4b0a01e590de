// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: "config.gradle"
buildscript {
    repositories {
        google()
        jcenter()
        maven { url 'https://jitpack.io' }
        maven { url "https://bugly.qq.com/maven/" }
        maven { url "https://tencent-tds-maven.pkg.coding.net/repository/shiply/repo" }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.0'
        classpath "com.tencent.bugly:tinker-support:1.2.3"
//        classpath 'com.github.dcendents:android-maven-gradle-plugin:1.3'
//        classpath 'com.github.dcendents:android-maven-gradle-plugin:1.5'
//        classpath 'com.jfrog.bintray.gradle:gradle-bintray-plugin:1.7.3'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}


task clean(type: Delete) {
    delete rootProject.buildDir
}



<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="item"
            type="com.dspread.pos.ui.printer.PrinterItemViewModel" />

    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        binding:onClickCommand="@{item.itemClick}">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp">

            <ImageView
                android:id="@+id/item_icon"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@{item.iconResId}" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@{item.titleId}"
                android:textSize="16sp" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>
</layout>
plugins {
    id 'com.android.application'
}

android {
    namespace 'com.dspread.pos_android_app'
    compileSdk 34

    signingConfigs {
        release {
            storeFile file('app.keystore')
            storePassword 'dspread'
            keyPassword 'dspread'
            keyAlias 'gundam_wing'
        }
        debug {
            storeFile file('app.keystore')
            storePassword 'dspread'
            keyPassword 'dspread'
            keyAlias 'gundam_wing'
        }
//        config {
//            keyAlias 'key0'
//            keyPassword '123789'
//            storeFile file('dspread.jks')
//            storePassword '123789'
//        }
    }

    defaultConfig {
        applicationId "com.dspread.pos_android_app"
        minSdk 24
        targetSdk 34
        versionCode 88
        versionName "6.2.3"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            // Set up supported SO library architecture
            abiFilters 'armeabi-v7a' //, 'x86', 'armeabi-v7a', 'x86_64', 'arm64-v8a'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    dataBinding {
        enabled true
    }
    //Important: Disable modern packaging formats (otherwise a generic APK will be generated)
    packagingOptions {
        doNotStrip '**/lib/*.so' // 保留所有SO文件
    }
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'androidx.gridlayout:gridlayout:1.0.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
    implementation 'androidx.navigation:navigation-fragment:2.4.1'
    implementation 'androidx.navigation:navigation-ui:2.4.1'
    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.25'

//    implementation 'com.dspread.print:dspread_print_sdk:1.4.9-beta'
//    implementation 'com.dspread.library:dspread_pos_sdk:7.1.2'
    implementation fileTree(dir: 'libs', include: ['*.aar', '*.jar'], exclude: [])
//    implementation project(':mvvmhabit')
//    api files('libs/dspread_pos_sdk_7.2.8.jar')
//    implementation 'androidx.lifecycle:lifecycle-extensions:2.0.0'
    api rootProject.ext.dependencies["lifecycle-extensions"]
    annotationProcessor rootProject.ext.dependencies["lifecycle-compiler"]
    //rx管理View的生命周期
    api(rootProject.ext.dependencies.rxlifecycle) {
        exclude group: 'com.android.support'
    }
    api(rootProject.ext.dependencies["rxlifecycle-components"]) {
        exclude group: 'com.android.support'
    }
    //rxbinding
    api(rootProject.ext.dependencies.rxbinding) {
        exclude group: 'com.android.support'
    }
    api(rootProject.ext.dependencies.bindingcollectionadapter) {
        exclude group: 'com.android.support'
    }
    api(rootProject.ext.dependencies["bindingcollectionadapter-recyclerview"]) {
        exclude group: 'com.android.support'
    }
    api(rootProject.ext.dependencies["bindingcollectionadapter-viewpager2"]) {
        exclude group: 'com.android.support'
    }
    //rx权限请求
    api(rootProject.ext.dependencies.rxpermissions) {
        exclude group: 'com.android.support'
    }

    api rootProject.ext.dependencies.rxjava
    api rootProject.ext.dependencies.rxandroid
    //network
    api rootProject.ext.dependencies.okhttp
    api rootProject.ext.dependencies.retrofit
    api rootProject.ext.dependencies["converter-gson"]
    api rootProject.ext.dependencies["adapter-rxjava"]

    implementation 'com.tencent.bugly:crashreport:4.1.9'
    implementation 'com.tencent.bugly:nativecrashreport:3.9.2'
    implementation 'com.alibaba:fastjson:1.2.83'

    implementation("com.tencent.shiply:upgrade:2.2.0")
    implementation("com.tencent.shiply:upgrade-ui:2.2.0") // Pop up UI related, if the business side customizes their own pop ups, they can not rely on this library
    implementation 'com.github.midorikocak:currency-picker-android:1.2.1'
}
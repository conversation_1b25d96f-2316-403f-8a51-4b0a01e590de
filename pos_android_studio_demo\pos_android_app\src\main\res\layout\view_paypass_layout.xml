<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/grey9"
        android:orientation="vertical">

        <!--==============pass=================-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_3"
            android:gravity="center"
            android:orientation="horizontal"
           >
            <EditText
                android:id="@+id/et_inputpin"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:inputType="textPassword"
                android:textSize="30dp"
                android:background="@color/graye3"
                android:cursorVisible="false"
                ></EditText>

        </LinearLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white">
            <GridView
                android:id="@+id/gv_pass"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:horizontalSpacing="@dimen/dp_line"
                android:verticalSpacing="@dimen/dp_line"
                android:numColumns="3"
                android:listSelector="@color/white"  />
        </RelativeLayout>

    </LinearLayout>

</RelativeLayout>

<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:binding="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="com.dspread.pos.ui.printer.PrinterViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/keyboard_background">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/printerWork_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:items="@{viewModel.items}"
            app:itemBinding="@{viewModel.itemBinding}" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
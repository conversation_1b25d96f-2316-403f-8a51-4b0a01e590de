<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
<LinearLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="@dimen/nav_header_height"
        android:background="@drawable/side_nav_bar"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:paddingTop="@dimen/activity_vertical_margin"
        android:paddingRight="@dimen/activity_horizontal_margin"
        android:paddingBottom="@dimen/activity_vertical_margin"
        android:theme="@style/ThemeOverlay.AppCompat.Dark">

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:contentDescription="@string/nav_header_desc"
                app:srcCompat="@mipmap/ic_dspread_logo" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:orientation="vertical"
                android:padding="5dp">
                <TextView
                    android:id="@+id/tv_appversion"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                    android:textSize="11sp"
                    />
                <View
                    android:id="@+id/v_appversion"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_1"
                    android:visibility="gone"
                    android:background="@color/white"
                    ></View>
                <TextView
                    android:id="@+id/device_connect_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textSize="11sp"
                    android:textAppearance="@style/TextAppearance.AppCompat.Body1">

                </TextView>
             <View
                 android:id="@+id/v_device_connect_type"
                 android:layout_width="match_parent"
                 android:layout_height="@dimen/dp_1"
                 android:background="@color/white"
                 android:visibility="gone"
                 ></View>

            </LinearLayout>



</LinearLayout>
</layout>
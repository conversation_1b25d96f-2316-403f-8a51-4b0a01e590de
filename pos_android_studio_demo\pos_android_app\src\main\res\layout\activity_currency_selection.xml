<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    
    <data>
        <variable
            name="viewModel"
            type="com.dspread.pos.ui.setting.device_config.DeviceConfigSelectionViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:layout_constraintTop_toTopOf="parent"
            app:navigationIcon="@mipmap/left"
            >
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="Select"
                android:textColor="?attr/colorOnPrimary"
                android:textSize="18sp"
                android:textStyle="bold" />
        </com.google.android.material.appbar.MaterialToolbar>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp"
            android:background="@color/white">

            <EditText
                android:id="@+id/search_edit_text"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:background="@drawable/search_background"
                android:drawableStart="@drawable/ic_search"
                android:drawablePadding="8dp"
                android:hint="pls choose"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:textSize="14sp"
                android:text="@={viewModel.searchText}"
                android:maxLines="1"
                android:imeOptions="actionSearch"/>
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/currency_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

    </LinearLayout>
</layout>
<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:binding="http://schemas.android.com/apk/res-auto">


    <data>
        <variable
            name="viewModel"
            type="com.dspread.pos.ui.setting.connection_settings.ConnectionSettingsViewModel" />
        <import type="android.view.View" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#F5F5F5"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 上半部分卡片：设备连接 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- 标题：已连接的设备 -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Connected Device"
                        android:textColor="#333333"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <!-- 设备名称和开关 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.deviceName}"
                                android:textColor="#333333"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.deviceConnected ? @string/str_connected : @string/str_disconnect}"
                                android:textColor="@{viewModel.deviceConnected ? @color/colorPrimary : @android:color/darker_gray}"
                                android:textSize="14sp"
                                android:layout_marginTop="4dp" />
                        </LinearLayout>

                        <Switch
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            binding:switchState="@{viewModel.deviceConnected}"
                            binding:onCheckedChangeCommand="@{viewModel.toggleDeviceCommand}" />
                    </LinearLayout>

                    <!-- 选择设备按钮 -->
                    <Button
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Choose Connection"
                        android:textColor="#FFFFFF"
                        android:padding="10dp"
                        android:textSize="16sp"
                        android:background="@drawable/button_background"
                        android:onClick="@{() -> viewModel.selectDeviceCommand.execute()}" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 下半部分卡片：交易设置 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- 交易类型 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical"
                        android:background="?attr/selectableItemBackground"
                        android:onClick="@{() -> viewModel.transactionTypeCommand.execute()}">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/transaction_type"
                                android:textColor="#333333"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.transactionType}"
                                android:textColor="#757575"
                                android:textSize="14sp"
                                android:layout_marginTop="4dp" />
                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_chevron_right" />
                    </LinearLayout>

                    <!-- 分隔线 -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E0E0E0" />

                    <!-- 卡片模式 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical"
                        android:background="?attr/selectableItemBackground"
                        android:onClick="@{() -> viewModel.cardModeCommand.execute()}">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/trans_mode"
                                android:textColor="#333333"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.cardMode}"
                                android:textColor="#757575"
                                android:textSize="14sp"
                                android:layout_marginTop="4dp" />
                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_chevron_right" />
                    </LinearLayout>

                    <!-- 分隔线 -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E0E0E0" />

                    <!-- 货币代码 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical"
                        android:background="?attr/selectableItemBackground"
                        android:onClick="@{() -> viewModel.currencyCodeCommand.execute()}">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/currency_code"
                                android:textColor="#333333"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.currencyCode}"
                                android:textColor="#757575"
                                android:textSize="14sp"
                                android:layout_marginTop="4dp" />
                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_chevron_right" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </ScrollView>
</layout>

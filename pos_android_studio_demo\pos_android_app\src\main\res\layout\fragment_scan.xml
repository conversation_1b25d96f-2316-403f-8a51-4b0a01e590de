<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:binding="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="com.dspread.pos.ui.scan.ScanViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/keyboard_background">

        <!-- 扫描结果区域 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/btn_scan"
            android:visibility="@{viewModel.hasResult ? View.VISIBLE : View.GONE}">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Scanning Result"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@{viewModel.scanResult}"
                        android:textIsSelectable="true"
                        android:textSize="16sp" />
                </ScrollView>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 扫描按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_scan"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:padding="12dp"
            android:text="@string/start_scan"
            android:textSize="16sp"
            app:cornerRadius="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            binding:onClickCommand="@{viewModel.startScanCommand}" />

        <!-- 加载进度条 -->
        <ProgressBar
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
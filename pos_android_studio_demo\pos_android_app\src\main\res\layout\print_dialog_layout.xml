<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/reunded_recyclerview"
    android:orientation="vertical"
    android:padding="10dp">


    <TextView
        android:id="@+id/tv_print_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical">

        <EditText
            android:id="@+id/et_input_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp_20"
            android:focusable="true"
            android:inputType="none"
            android:focusableInTouchMode="true"
            android:padding="@dimen/dp_5"
            android:background="@color/white"
            tools:ignore="LabelFor,SpeakableTextPresentCheck,TextFields,TouchTargetSizeCheck,VisualLintTextFieldSize"
            android:importantForAutofill="no" />


    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/graye3"/>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_print_cancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:padding="5dp"
            android:text="@string/cancel"
            android:textSize="16sp"/>

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/graye3"/>

        <TextView
            android:id="@+id/tv_print_confirm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:padding="5dp"
            android:textSize="16sp"
            android:text="@string/confirm"/>
    </LinearLayout>
</LinearLayout>
<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:binding="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View"/>
        <variable
            name="viewModel"
            type="com.dspread.pos.ui.printer.activities.base.BasePrinterViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:layout_constraintTop_toTopOf="parent"
            app:navigationIcon="@mipmap/left"
            >
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@{viewModel.title}"
                android:textColor="?attr/colorOnPrimary"
                android:textSize="18sp"
                android:textStyle="bold" />
        </com.google.android.material.appbar.MaterialToolbar>


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            app:layout_constraintBottom_toTopOf="@id/btn_print" >
            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fillViewport="true">

                <LinearLayout
                    android:id="@+id/content_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"/>

            </ScrollView>
        </FrameLayout>


        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_print"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:padding="14dp"
            android:text="Print"
            android:background="@drawable/rounded_button"
            app:layout_constraintBottom_toBottomOf="parent"
            android:enabled="@{viewModel.isLoading ? false:true}"
            binding:onClickCommand="@{viewModel.printCommand}" />

        <ProgressBar
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="@{viewModel.isLoading ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
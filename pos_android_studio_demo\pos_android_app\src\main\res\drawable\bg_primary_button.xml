<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#4B0082" />  <!-- 深紫色 -->
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#6A0DAD" />  <!-- 紫色 -->
            <corners android:radius="8dp" />
        </shape>
    </item>
</selector>
<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:binding="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="viewModel"
            type="com.dspread.pos.ui.home.HomeViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/home_layout"
        android:orientation="vertical"
        android:background="@color/floralwhite"
        android:clickable="true"
        android:focusable="true"
        tools:context=".ui.home.HomeFragment">

        <LinearLayout
            android:id="@+id/topLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="end"
            android:padding="16dp">

            <TextView
                android:id="@+id/txt_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="3dp"
                android:text="@{viewModel.amount}"
                android:textColor="@color/black"
                android:textSize="33sp" />
        </LinearLayout>

<!--        <com.dspread.demoui.widget.amountKeyboard.MyKeyBoardView-->
<!--            android:id="@+id/keyboard_view2"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="bottom"-->
<!--            android:focusable="true"-->
<!--            android:focusableInTouchMode="true"-->
<!--            android:keyBackground="@drawable/bg_keyboardview"-->
<!--            android:keyPreviewOffset="0dp"-->
<!--            android:keyTextColor="#000"-->
<!--            android:paddingTop="0dp"-->
<!--            android:shadowColor="#fff"-->
<!--            android:shadowRadius="0.0"/>-->
    </LinearLayout>
</layout>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_paytype"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="5dp"
        android:background="@drawable/item_style"
        android:gravity="center"
        android:padding="10dp"
        android:text="TextView"
        android:textColor="@color/black"
        android:textSize="20dp"
        tools:ignore="HardcodedText,SpUsage,TextSizeCheck" />
</LinearLayout>
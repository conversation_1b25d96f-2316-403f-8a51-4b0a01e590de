<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_LOGS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />

    <!-- Required permissions for Android 12 and above versions -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation"
        tools:targetApi="s" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />

    <!-- Location permissions -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />


    <application
        android:name="com.dspread.pos.TerminalApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_dspread_logo"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/Theme.Despreaddemo1"
        tools:replace="android:theme"
        android:networkSecurityConfig="@xml/network_security_config">
        <meta-data
            android:name="BUGLY_APPID"
            android:value="b2d80aa171" />
        <activity
            android:name="com.dspread.pos.ui.main.MainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:theme="@style/Theme.Despreaddemo1">

             <intent-filter>
             <action android:name="android.intent.action.MAIN" />


             <category android:name="android.intent.category.LAUNCHER" />
             </intent-filter>
        </activity>
        <activity
            android:name="com.dspread.pos.ui.payment.PaymentActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.MaterialComponents.Light.NoActionBar"/>
        <activity
            android:name="com.dspread.pos.ui.printer.activities.PrintTextActivity"
            android:exported="false" />
        <activity
            android:name="com.dspread.pos.ui.printer.activities.base.PrinterBaseActivity"
            android:exported="false" />
        <activity
            android:name="com.dspread.pos.ui.printer.activities.BarCodeActivity"
            android:exported="false" />
        <activity
            android:name="com.dspread.pos.ui.printer.activities.QRCodeActivity"
            android:exported="false" />
        <activity
            android:name="com.dspread.pos.ui.printer.activities.BitmapActivity"
            android:exported="false" />
        <activity
            android:name="com.dspread.pos.ui.printer.activities.PrintFunctionMultiActivity"
            android:exported="false" />
        <activity
            android:name="com.dspread.pos.ui.printer.activities.PrintTicketActivity"
            android:exported="false" />
        <activity
            android:name="com.dspread.pos.ui.printer.activities.PrinterStatusActivity"
            android:exported="false" />
        <activity
            android:name="com.dspread.pos.ui.setting.device_selection.DeviceSelectionActivity"
            android:exported="false"
            />
        <activity
            android:name="com.dspread.pos.ui.setting.device_config.DeviceConfigActivity"
            android:exported="false"/>
    </application>

</manifest>
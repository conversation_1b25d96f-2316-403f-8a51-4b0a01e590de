<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/reunded_recyclerview"
    android:orientation="vertical"
    android:padding="10dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/prompt_message"
        android:textColor="@color/teal_200"
        android:visibility="gone"
        android:textSize="20dp"></TextView>

    <TextView
        android:id="@+id/tvInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text=""
        android:gravity="center"
        android:textSize="20dp"></TextView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:weightSum="3"
        android:padding="5dp"
        android:gravity="center"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btnCancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="16dp"
            android:background="@drawable/rounded_button"
            android:text="@string/select_dialog_cancel"
            android:textColor="@color/white">

        </Button>

        <View
            android:id="@+id/view_v"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            android:layout_weight="1"></View>

        <Button
            android:id="@+id/btnConfirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="16dp"
            android:background="@drawable/rounded_button"
            android:text="@string/select_dialog_confirm"
            android:textColor="@color/white"
            >

        </Button>

    </LinearLayout>

</LinearLayout>
<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="me.goldze.mvvmhabit.base.BaseViewModel" />

        <variable
            name="viewModel"
            type="BaseViewModel" />

    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:tabGravity="fill"
            app:tabIndicatorColor="@color/colorPrimary"
            app:tabSelectedTextColor="@color/colorPrimary"
            app:tabTextColor="@android:color/black" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#F0F0F0" />

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />
    </LinearLayout>
</layout>
<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:binding="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="com.dspread.pos.ui.printer.activities.PrintTextViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 打印设置卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 对齐方式设置 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:padding="16dp"
                    android:gravity="center_vertical"
                    binding:onClickCommand="@{viewModel.onAlignClick}">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:text="@string/set_align"
                        android:textSize="16sp" />

                    <TextView
                        android:text="@{viewModel.alignText}"
                        style="@style/TextviewPrint"/>
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/darkgrey" />

                <!-- 字体样式设置 -->
                <LinearLayout
                    android:gravity="center_vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:padding="16dp"
                    binding:onClickCommand="@{viewModel.onFontStyleClick}">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:text="@string/set_font_style"
                        android:textSize="16sp" />

                    <TextView
                        style="@style/TextviewPrint"
                        android:text="@{viewModel.fontStyle}"
                        />
                </LinearLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/darkgrey" />

                <!-- 字体size设置 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:padding="16dp"
                    android:gravity="center_vertical"
                    binding:onClickCommand="@{viewModel.onFontSizeClick}">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:text="@string/set_font_size"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/txt_size"
                        style="@style/TextviewPrint"
                        android:text="@{viewModel.textSize}" />
                </LinearLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/darkgrey" />

                <!-- 字体max size设置 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:padding="16dp"
                    android:gravity="center_vertical"
                    binding:onClickCommand="@{viewModel.onMaxHeightClick}">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:text="@string/content_maxHeight"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/txt_maxheight"
                        style="@style/TextviewPrint"
                        android:text="@{viewModel.maxHeight}"
                        />
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- 打印内容输入框 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:padding="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="top"
                android:hint="@string/print_content"
                android:inputType="textMultiLine"
                android:text="@{viewModel.printContent}" />
        </com.google.android.material.textfield.TextInputLayout>

    </LinearLayout>
</layout>
<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:binding="http://schemas.android.com/tools">
    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.dspread.pos.ui.payment.PaymentViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <include layout="@layout/activity_title" app:viewModel="@{viewModel}"/>

        <!-- 添加加载框布局 -->
        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            android:visibility="@{viewModel.isLoading ? View.VISIBLE : View.GONE}">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="16dp">

                <ProgressBar
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:indeterminateTint="@color/colorPrimary"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@{viewModel.loadingText}"
                    android:textSize="16sp"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 原有的卡片布局 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            android:visibility="@{viewModel.showPinpad ? View.VISIBLE : View.GONE}">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <EditText
                    android:id="@+id/pinpadEditText"
                    android:background="@drawable/edittext_no_cursor"
                    android:textCursorDrawable="@null"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:cursorVisible="false"
                    android:clickable="false"
                    android:longClickable="false"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textSize="20sp"
                    />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <!-- 刷卡动画区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:visibility="@{viewModel.isWaiting ? View.VISIBLE : View.GONE}">

                <pl.droidsonroids.gif.GifImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/checkcard"/>
            </LinearLayout>

            <!-- 交易信息区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="@{viewModel.showResultStatus ? View.VISIBLE : View.GONE}">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:padding="16dp">

                    <ImageView
                        android:id="@+id/iv_status"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@{viewModel.isSuccess ? @drawable/ic_check_circle : @drawable/ic_error_circle}"
                        android:layout_marginEnd="8dp"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/transaction_result"
                        android:textColor="@{viewModel.isSuccess ? @color/green : @color/red}"
                        android:textSize="24sp"/>
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gainsboro"/>

                <ScrollView
                    android:id="@+id/scv_text"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:layout_margin="16dp"
                    android:layout_gravity="center">

                    <TextView
                        android:id="@+id/tv_receipt"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_receipt"
                        android:fontFamily="monospace"
                        android:padding="16dp"
                        android:textSize="16sp"
                        android:gravity="start"
                        android:textColor="@color/text_primary"
                        android:textIsSelectable="true"
                        android:textAlignment="viewStart"
                        android:text="@{viewModel.transactionResult}"
                        />
                </ScrollView>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                android:orientation="horizontal">
                <com.google.android.material.button.MaterialButton
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="@string/continue_trading"
                    android:textColor="@color/white"
                    android:background="@drawable/bg_primary_button"
                    android:layout_marginEnd="8dp"
                    app:cornerRadius="8dp"
                    android:textSize="14sp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:enabled="@{!viewModel.isPrinting}"
                    binding:onClickCommand="@{viewModel.continueTxnsCommand}"/>
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_send_receipt"
                    android:visibility="gone"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/bg_primary_button"
                    android:text="@string/send_receipt"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:gravity="center"
                    app:cornerRadius="8dp"
                    android:enabled="@{!viewModel.isPrinting}"
                    binding:onClickCommand="@{viewModel.sendReceiptCommand}"/>
            </LinearLayout>
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>
</layout>